import os
import subprocess
import argparse

# Parse command-line arguments
parser = argparse.ArgumentParser(description='Convert audio files to 16kHz single-channel WAV.')
parser.add_argument('input_dir', type=str, help='Input directory containing audio files')
parser.add_argument('output_dir', type=str, help='Output directory for converted WAV files')
args = parser.parse_args()

# Use provided directories
input_dir = args.input_dir
os.makedirs(args.output_dir, exist_ok=True)

# Supported audio extensions
audio_extensions = ('.mp3', '.wav', '.ogg')

# Iterate through files in the input directory
for filename in os.listdir(input_dir):
    if filename.lower().endswith(audio_extensions):
        input_path = os.path.join(input_dir, filename)
        # Output filename: replace extension with .wav
        output_filename = os.path.splitext(filename)[0] + '.wav'
        output_path = os.path.join(args.output_dir, output_filename)
        
        # Skip if output already exists (optional, to avoid re-processing)
        if os.path.exists(output_path):
            print(f"Skipping {filename} as output already exists.")
            continue
        
        # ffmpeg command
        cmd = ['ffmpeg.exe', '-i', input_path, '-ar', '16000', '-ac', '1', '-t', '2.0', output_path]
        
        try:
            subprocess.run(cmd, check=True)
            print(f"Converted {filename} to {output_filename}")
        except subprocess.CalledProcessError as e:
            print(f"Error converting {filename}: {e}")
